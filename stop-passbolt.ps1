# Stop Passbolt Password Manager <PERSON><PERSON>t

Write-Host "Stopping Passbolt Password Manager..." -ForegroundColor Yellow

try {
    docker compose -f docker-compose-ce.yaml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Passbolt stopped successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Your data is safely stored in Docker volumes and will be available when you restart Passbolt." -ForegroundColor Cyan
        Write-Host ""
        Write-Host "To start Passbolt again, run:" -ForegroundColor White
        Write-Host "  .\start-passbolt.ps1" -ForegroundColor Cyan
    } else {
        Write-Host "Error stopping Passbolt containers." -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}
