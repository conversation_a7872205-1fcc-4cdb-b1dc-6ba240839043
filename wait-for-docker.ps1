# Wait for Docker Desktop to be ready

Write-Host "Waiting for Docker Desktop to start..." -ForegroundColor Yellow
Write-Host "This usually takes 1-2 minutes..." -ForegroundColor Cyan

$maxWait = 300  # 5 minutes max
$waited = 0
$interval = 10

do {
    Start-Sleep -Seconds $interval
    $waited += $interval
    
    try {
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "Docker Desktop is ready! ✓" -ForegroundColor Green
            Write-Host ""
            Write-Host "Now you can run:" -ForegroundColor Cyan
            Write-Host "  .\start-passbolt.ps1" -ForegroundColor White
            exit 0
        }
    } catch {
        # Continue waiting
    }
    
    Write-Host "Still waiting... ($waited/$maxWait seconds)" -ForegroundColor Yellow
    
} while ($waited -lt $maxWait)

Write-Host ""
Write-Host "Docker Desktop is taking longer than expected to start." -ForegroundColor Red
Write-Host "Please check:" -ForegroundColor Yellow
Write-Host "1. Docker Desktop icon in system tray" -ForegroundColor White
Write-Host "2. Any error messages from Dock<PERSON> Desktop" -ForegroundColor White
Write-Host "3. Try restarting Docker Desktop manually" -ForegroundColor White
