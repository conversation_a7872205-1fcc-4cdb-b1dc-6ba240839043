# Check Passbolt Status Script

Write-Host "Checking Passbolt Status..." -ForegroundColor Green

# Check Docker status
Write-Host "`n=== Docker Status ===" -ForegroundColor Cyan
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker: Running ✓" -ForegroundColor Green
    } else {
        Write-Host "Docker: Not running ✗" -ForegroundColor Red
        Write-Host "Please start Docker Desktop first." -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "Docker: Error checking status ✗" -ForegroundColor Red
    exit 1
}

# Check container status
Write-Host "`n=== Container Status ===" -ForegroundColor Cyan
try {
    $containers = docker compose -f docker-compose-ce.yaml ps --format json | ConvertFrom-Json
    
    if ($containers.Count -eq 0) {
        Write-Host "No Passbolt containers found." -ForegroundColor Yellow
        Write-Host "Run .\start-passbolt.ps1 to start Passbolt." -ForegroundColor Cyan
    } else {
        foreach ($container in $containers) {
            $status = if ($container.State -eq "running") { "✓" } else { "✗" }
            $color = if ($container.State -eq "running") { "Green" } else { "Red" }
            Write-Host "$($container.Service): $($container.State) $status" -ForegroundColor $color
        }
    }
} catch {
    Write-Host "Error checking container status: $_" -ForegroundColor Red
}

# Check if Passbolt web interface is accessible
Write-Host "`n=== Web Interface Status ===" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "https://localhost" -SkipCertificateCheck -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        Write-Host "Web interface: Accessible ✓" -ForegroundColor Green
        Write-Host "URL: https://localhost" -ForegroundColor Cyan
    } else {
        Write-Host "Web interface: Not accessible ✗" -ForegroundColor Red
    }
} catch {
    Write-Host "Web interface: Not accessible ✗" -ForegroundColor Red
    Write-Host "This is normal if containers are still starting up." -ForegroundColor Yellow
}

Write-Host "`n=== Quick Commands ===" -ForegroundColor Cyan
Write-Host "Start Passbolt:    .\start-passbolt.ps1" -ForegroundColor White
Write-Host "Stop Passbolt:     .\stop-passbolt.ps1" -ForegroundColor White
Write-Host "Create admin:      .\create-admin.ps1" -ForegroundColor White
Write-Host "View logs:         docker compose -f docker-compose-ce.yaml logs -f" -ForegroundColor White
Write-Host "Check this status: .\status.ps1" -ForegroundColor White
