# Passbolt Startup Script
# This script will start your Passbolt password manager

Write-Host "Starting Passbolt Password Manager..." -ForegroundColor Green

# Check if Dock<PERSON> is running
Write-Host "Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
        Write-Host "You can start Docker Desktop from the Start menu or by running:" -ForegroundColor Yellow
        Write-Host '  Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"' -ForegroundColor Cyan
        Write-Host "Wait for Docker Desktop to fully start (you'll see the whale icon in the system tray), then run this script again." -ForegroundColor Yellow
        exit 1
    }
    Write-Host "Docker is running!" -ForegroundColor Green
} catch {
    Write-Host "Error checking Docker status: $_" -ForegroundColor Red
    exit 1
}

# Start Passbolt containers
Write-Host "Starting Passbolt containers..." -ForegroundColor Yellow
docker compose -f docker-compose-ce.yaml up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "Passbolt containers started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Waiting for containers to be ready (this may take 2-3 minutes)..." -ForegroundColor Yellow
    
    # Wait for containers to be healthy
    $maxWait = 180  # 3 minutes
    $waited = 0
    $interval = 10
    
    do {
        Start-Sleep -Seconds $interval
        $waited += $interval
        
        $status = docker compose -f docker-compose-ce.yaml ps --format json | ConvertFrom-Json
        $allRunning = $true
        
        foreach ($container in $status) {
            if ($container.State -ne "running") {
                $allRunning = $false
                break
            }
        }
        
        if ($allRunning) {
            Write-Host "All containers are running!" -ForegroundColor Green
            break
        }
        
        Write-Host "Still waiting... ($waited/$maxWait seconds)" -ForegroundColor Yellow
        
    } while ($waited -lt $maxWait)
    
    if ($waited -ge $maxWait) {
        Write-Host "Containers are taking longer than expected to start. Check the logs:" -ForegroundColor Yellow
        Write-Host "  docker compose -f docker-compose-ce.yaml logs" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "=== NEXT STEPS ===" -ForegroundColor Cyan
    Write-Host "1. Create your admin user by running:" -ForegroundColor White
    Write-Host '   .\create-admin.ps1' -ForegroundColor Cyan
    Write-Host ""
    Write-Host "2. Or manually with:" -ForegroundColor White
    Write-Host '   docker compose -f docker-compose-ce.yaml exec passbolt su -m -c "/usr/share/php/passbolt/bin/cake passbolt register_user -u YOUR_EMAIL -f YOUR_NAME -l YOUR_LASTNAME -r admin" -s /bin/sh www-data' -ForegroundColor Cyan
    Write-Host ""
    Write-Host "3. Then open your browser to: https://localhost" -ForegroundColor White
    Write-Host "   (Accept the security warning for the self-signed certificate)" -ForegroundColor Yellow
    
} else {
    Write-Host "Failed to start Passbolt containers. Check the error messages above." -ForegroundColor Red
    Write-Host "You can check the logs with:" -ForegroundColor Yellow
    Write-Host "  docker compose -f docker-compose-ce.yaml logs" -ForegroundColor Cyan
}
