# Passbolt Backup Script
# This script creates a complete backup of your Passbolt installation

Write-Host "Creating Passbolt Backup..." -ForegroundColor Green

# Create backup directory
$backupDir = "passbolt-backup-$(Get-Date -Format 'yyyy-MM-dd-HHmm')"
New-Item -ItemType Directory -Path $backupDir -Force

Write-Host "Backup directory: $backupDir" -ForegroundColor Cyan

# Stop containers to ensure data consistency
Write-Host "Stopping Passbolt containers..." -ForegroundColor Yellow
docker compose -f docker-compose-ce.yaml down

# Create volume backups
Write-Host "Backing up database volume..." -ForegroundColor Yellow
docker run --rm -v drpass_database_volume:/data -v "${PWD}/${backupDir}:/backup" alpine tar czf /backup/database.tar.gz -C /data .

Write-Host "Backing up GPG keys volume..." -ForegroundColor Yellow
docker run --rm -v drpass_gpg_volume:/data -v "${PWD}/${backupDir}:/backup" alpine tar czf /backup/gpg.tar.gz -C /data .

Write-Host "Backing up JWT tokens volume..." -ForegroundColor Yellow
docker run --rm -v drpass_jwt_volume:/data -v "${PWD}/${backupDir}:/backup" alpine tar czf /backup/jwt.tar.gz -C /data .

# Copy configuration files
Write-Host "Backing up configuration files..." -ForegroundColor Yellow
Copy-Item "docker-compose-ce.yaml" "$backupDir/"
Copy-Item "README.md" "$backupDir/" -ErrorAction SilentlyContinue
Copy-Item "*.ps1" "$backupDir/" -ErrorAction SilentlyContinue

# Create database dump
Write-Host "Creating database dump..." -ForegroundColor Yellow
docker compose -f docker-compose-ce.yaml up -d db
Start-Sleep 10
docker compose -f docker-compose-ce.yaml exec db mysqldump -u passbolt -pP4ssb0lt passbolt > "$backupDir/database-dump.sql"
docker compose -f docker-compose-ce.yaml down

Write-Host ""
Write-Host "Backup completed successfully!" -ForegroundColor Green
Write-Host "Backup location: $backupDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "To transfer to another server:" -ForegroundColor White
Write-Host "1. Copy the entire '$backupDir' folder to the new server" -ForegroundColor Yellow
Write-Host "2. Run the restore script on the new server" -ForegroundColor Yellow
