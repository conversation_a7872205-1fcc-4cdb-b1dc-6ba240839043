# Update Passbolt Configuration for New Server

param(
    [Parameter(Mandatory=$true)]
    [string]$NewServerUrl
)

Write-Host "Updating Passbolt configuration for new server..." -ForegroundColor Green
Write-Host "New URL: $NewServerUrl" -ForegroundColor Cyan

# Update docker-compose.yaml
$dockerCompose = Get-Content "docker-compose-ce.yaml"
$dockerCompose = $dockerCompose -replace "APP_FULL_BASE_URL:.*", "APP_FULL_BASE_URL: $NewServerUrl"
$dockerCompose | Set-Content "docker-compose-ce.yaml"

Write-Host "Configuration updated!" -ForegroundColor Green
Write-Host ""
Write-Host "Restarting Passbolt with new configuration..." -ForegroundColor Yellow

# Restart containers
docker compose -f docker-compose-ce.yaml down
docker compose -f docker-compose-ce.yaml up -d

Write-Host ""
Write-Host "Passbolt is now running on: $NewServerUrl" -ForegroundColor Green
