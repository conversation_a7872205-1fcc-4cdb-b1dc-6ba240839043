# Passbolt Restore Script
# This script restores your Passbolt installation from a backup

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath
)

Write-Host "Restoring Passbolt from backup..." -ForegroundColor Green
Write-Host "Backup path: $BackupPath" -ForegroundColor Cyan

if (-not (Test-Path $BackupPath)) {
    Write-Host "Backup path not found: $BackupPath" -ForegroundColor Red
    exit 1
}

# Copy configuration files
Write-Host "Restoring configuration files..." -ForegroundColor Yellow
Copy-Item "$BackupPath/docker-compose-ce.yaml" "." -Force
Copy-Item "$BackupPath/*.ps1" "." -Force -ErrorAction SilentlyContinue
Copy-Item "$BackupPath/README.md" "." -Force -ErrorAction SilentlyContinue

# Create Docker volumes
Write-Host "Creating Docker volumes..." -ForegroundColor Yellow
docker volume create drpass_database_volume
docker volume create drpass_gpg_volume
docker volume create drpass_jwt_volume

# Restore volume data
Write-Host "Restoring database volume..." -ForegroundColor Yellow
docker run --rm -v drpass_database_volume:/data -v "${BackupPath}:/backup" alpine tar xzf /backup/database.tar.gz -C /data

Write-Host "Restoring GPG keys volume..." -ForegroundColor Yellow
docker run --rm -v drpass_gpg_volume:/data -v "${BackupPath}:/backup" alpine tar xzf /backup/gpg.tar.gz -C /data

Write-Host "Restoring JWT tokens volume..." -ForegroundColor Yellow
docker run --rm -v drpass_jwt_volume:/data -v "${BackupPath}:/backup" alpine tar xzf /backup/jwt.tar.gz -C /data

# Start Passbolt
Write-Host "Starting Passbolt..." -ForegroundColor Yellow
docker compose -f docker-compose-ce.yaml up -d

Write-Host ""
Write-Host "Restore completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Your Passbolt should be accessible at the URL configured in docker-compose-ce.yaml" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor White
Write-Host "1. Update APP_FULL_BASE_URL in docker-compose-ce.yaml for the new server" -ForegroundColor Yellow
Write-Host "2. Restart containers: docker compose -f docker-compose-ce.yaml restart" -ForegroundColor Yellow
Write-Host "3. Set up ngrok or domain for public access if needed" -ForegroundColor Yellow
