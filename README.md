# Personal Passbolt Password Manager

This is your personal Passbolt Community Edition password manager running on Docker.

## Prerequisites

- Docker Desktop installed and running
- At least 2GB of available RAM
- Ports 80 and 443 available on your system

## Quick Start

### 1. Start the Passbolt containers

```bash
docker compose -f docker-compose-ce.yaml up -d
```

### 2. Wait for containers to be ready (about 2-3 minutes)

Check the status:
```bash
docker compose -f docker-compose-ce.yaml ps
```

### 3. Create your first admin user

Replace the placeholders with your information:
```bash
docker compose -f docker-compose-ce.yaml exec passbolt su -m -c "/usr/share/php/passbolt/bin/cake passbolt register_user -u YOUR_EMAIL -f YOUR_FIRST_NAME -l YOUR_LAST_NAME -r admin" -s /bin/sh www-data
```

Example:
```bash
docker compose -f docker-compose-ce.yaml exec passbolt su -m -c "/usr/share/php/passbolt/bin/cake passbolt register_user -u admin@localhost -f John -l Doe -r admin" -s /bin/sh www-data
```

### 4. Access Passbolt

Open your browser and go to: https://localhost

**Note**: You'll see a security warning because we're using a self-signed certificate. Click "Advanced" and "Proceed to localhost" to continue.

### 5. Complete the setup

Follow the registration link provided by the previous command to complete your admin user setup.

## Configuration

### Email Settings

To receive email notifications, update the email settings in `docker-compose-ce.yaml`:

- `EMAIL_TRANSPORT_DEFAULT_HOST`: Your SMTP server
- `EMAIL_TRANSPORT_DEFAULT_PORT`: SMTP port (usually 587 for TLS)
- `EMAIL_TRANSPORT_DEFAULT_USERNAME`: Your email username
- `EMAIL_TRANSPORT_DEFAULT_PASSWORD`: Your email password
- `EMAIL_DEFAULT_FROM`: The "from" email address

### Custom Domain

To use a custom domain instead of localhost:

1. Update `APP_FULL_BASE_URL` in `docker-compose-ce.yaml`
2. Add the domain to your hosts file (Windows: `C:\Windows\System32\drivers\etc\hosts`)

## Management Commands

### Stop Passbolt
```bash
docker compose -f docker-compose-ce.yaml down
```

### Start Passbolt
```bash
docker compose -f docker-compose-ce.yaml up -d
```

### View logs
```bash
docker compose -f docker-compose-ce.yaml logs -f passbolt
```

### Backup data
```bash
docker compose -f docker-compose-ce.yaml exec db mysqldump -u passbolt -pP4ssb0lt passbolt > backup.sql
```

### Update Passbolt
1. Stop the containers: `docker compose -f docker-compose-ce.yaml down`
2. Update the image version in `docker-compose-ce.yaml`
3. Pull new images: `docker compose -f docker-compose-ce.yaml pull`
4. Start containers: `docker compose -f docker-compose-ce.yaml up -d`

## Troubleshooting

### Container won't start
- Check if ports 80 and 443 are available
- Ensure Docker Desktop is running
- Check logs: `docker compose -f docker-compose-ce.yaml logs`

### Can't access the web interface
- Wait 2-3 minutes for full startup
- Check container status: `docker compose -f docker-compose-ce.yaml ps`
- Try accessing http://localhost instead of https

### Forgot admin password
Create a new admin user using the registration command above.

## Security Notes

- This setup uses a self-signed SSL certificate
- Default database password is in the docker-compose file
- For production use, consider:
  - Using proper SSL certificates
  - Changing default passwords
  - Setting up proper email configuration
  - Regular backups

## Data Location

Your Passbolt data is stored in Docker volumes:
- `drpass_database_volume`: Database data
- `drpass_gpg_volume`: GPG keys
- `drpass_jwt_volume`: JWT tokens

These volumes persist even when containers are stopped.
