# Simple script to update Passbolt URL

param(
    [string]$NewUrl = "http://localhost"
)

Write-Host "Updating Passbolt URL to: $NewUrl" -ForegroundColor Green

# Check if docker-compose file exists
if (-not (Test-Path "docker-compose-ce.yaml")) {
    Write-Host "Error: docker-compose-ce.yaml not found in current directory" -ForegroundColor Red
    Write-Host "Make sure you're in the correct directory (where your Passbolt files are)" -ForegroundColor Yellow
    exit 1
}

# Read the file
$content = Get-Content "docker-compose-ce.yaml"

# Update the URL
$updatedContent = $content -replace "APP_FULL_BASE_URL:.*", "APP_FULL_BASE_URL: $NewUrl"

# Write back to file
$updatedContent | Set-Content "docker-compose-ce.yaml"

Write-Host "URL updated successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Current configuration:" -ForegroundColor Cyan
$updatedContent | Where-Object { $_ -match "APP_FULL_BASE_URL" }
Write-Host ""
Write-Host "Next step: Restart Passbolt with the new URL" -ForegroundColor Yellow
Write-Host "Run: .\start-passbolt.ps1" -ForegroundColor Cyan
