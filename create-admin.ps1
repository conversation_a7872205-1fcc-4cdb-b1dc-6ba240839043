# Create Passbolt Admin User Script

Write-Host "Creating Passbolt Admin User..." -ForegroundColor Green

# Get user input
$email = Read-Host "Enter your email address"
$firstName = Read-Host "Enter your first name"
$lastName = Read-Host "Enter your last name"

# Validate input
if ([string]::IsNullOrWhiteSpace($email) -or 
    [string]::IsNullOrWhiteSpace($firstName) -or 
    [string]::IsNullOrWhiteSpace($lastName)) {
    Write-Host "All fields are required!" -ForegroundColor Red
    exit 1
}

# Check if containers are running
Write-Host "Checking if Passbolt containers are running..." -ForegroundColor Yellow
$status = docker compose -f docker-compose-ce.yaml ps --format json | ConvertFrom-Json
$passboltRunning = $false

foreach ($container in $status) {
    if ($container.Service -eq "passbolt" -and $container.State -eq "running") {
        $passboltRunning = $true
        break
    }
}

if (-not $passboltRunning) {
    Write-Host "Passbolt container is not running. Please start it first with:" -ForegroundColor Red
    Write-Host "  .\start-passbolt.ps1" -ForegroundColor Cyan
    exit 1
}

# Create the admin user
Write-Host "Creating admin user..." -ForegroundColor Yellow
Write-Host "Email: $email" -ForegroundColor White
Write-Host "Name: $firstName $lastName" -ForegroundColor White

$command = "/usr/share/php/passbolt/bin/cake passbolt register_user -u `"$email`" -f `"$firstName`" -l `"$lastName`" -r admin"

try {
    $result = docker compose -f docker-compose-ce.yaml exec passbolt su -m -c $command -s /bin/sh www-data
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Admin user created successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "=== IMPORTANT ===" -ForegroundColor Red
        Write-Host "Copy the registration link from the output above and paste it in your browser to complete the setup." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "The link should look like:" -ForegroundColor White
        Write-Host "https://localhost/setup/install/[UUID]/[UUID]" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "After completing the registration, you can access Passbolt at:" -ForegroundColor White
        Write-Host "https://localhost" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Note: You'll see a security warning because we're using a self-signed certificate." -ForegroundColor Yellow
        Write-Host "Click 'Advanced' and 'Proceed to localhost' to continue." -ForegroundColor Yellow
    } else {
        Write-Host "Failed to create admin user. Error details above." -ForegroundColor Red
    }
} catch {
    Write-Host "Error creating admin user: $_" -ForegroundColor Red
}
